﻿using Core.DataAccess;
using Core.Utilities.Paging;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IMembershiptypeDal:IEntityRepository<MembershipType>
    {
        List<PackageWithCountDto> GetPackagesByBranch(string branch);
        PaginatedResult<MembershipType> GetAllPaginated(MembershipTypePagingParameters parameters);
        List<BranchGetAllDto> GetBranchesAndTypes();
    }
}
