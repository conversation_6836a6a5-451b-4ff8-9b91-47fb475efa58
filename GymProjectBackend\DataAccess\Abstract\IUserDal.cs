﻿using Core.DataAccess;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace DataAccess.Abstract
{
    public interface IUserDal:IEntityRepository<User>
    {
        List<OperationClaim> GetClaims(User user);
        List<User> GetNonMembers();
        List<User> GetNonMembersPaginated(int page, int pageSize, string searchTerm);
        int GetNonMembersCount(string searchTerm);
        IResult SyncCompanyUserData(User updatedUser, User oldUser);
    }
}
